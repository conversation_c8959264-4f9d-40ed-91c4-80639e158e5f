class VideoProcessor {
    constructor() {
        this.ffmpeg = null;
        this.isLoaded = false;
        this.isProcessing = false;
    }

    // Initialize FFmpeg
    async initialize(onProgress) {
        if (this.isLoaded) return;

        try {
            // Create FFmpeg instance
            this.ffmpeg = new FFmpeg();
            
            // Set up progress callback
            if (onProgress) {
                this.ffmpeg.on('progress', ({ progress, time }) => {
                    onProgress(Math.round(progress * 100), `Processing... ${time}ms`);
                });
            }

            // Load FFmpeg
            const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.4/dist/umd';
            await this.ffmpeg.load({
                coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
                wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
            });

            this.isLoaded = true;
            console.log('FFmpeg loaded successfully');
        } catch (error) {
            throw new Error(`Failed to initialize FFmpeg: ${error.message}`);
        }
    }

    // Generate random video combinations
    async generateRandomVideos(videoFiles, targetDuration, quantity, onProgress) {
        if (!this.isLoaded) {
            throw new Error('FFmpeg not initialized');
        }

        if (this.isProcessing) {
            throw new Error('Already processing videos');
        }

        this.isProcessing = true;
        const results = [];

        try {
            for (let i = 0; i < quantity; i++) {
                if (onProgress) {
                    onProgress(0, `Generating video ${i + 1} of ${quantity}...`);
                }

                const result = await this.createRandomVideo(
                    videoFiles, 
                    targetDuration, 
                    i + 1,
                    (progress, message) => {
                        const overallProgress = ((i / quantity) * 100) + (progress / quantity);
                        if (onProgress) {
                            onProgress(overallProgress, `Video ${i + 1}: ${message}`);
                        }
                    }
                );
                
                results.push(result);
            }

            return results;
        } finally {
            this.isProcessing = false;
        }
    }

    // Create a single random video
    async createRandomVideo(videoFiles, targetDuration, videoNumber, onProgress) {
        const selectedClips = this.selectRandomClips(videoFiles, targetDuration);
        
        if (onProgress) {
            onProgress(10, 'Loading video files...');
        }

        // Load video files into FFmpeg
        const inputFiles = [];
        for (let i = 0; i < selectedClips.length; i++) {
            const clip = selectedClips[i];
            const videoData = await this.loadVideoFile(clip.file);
            const inputName = `input${i}.${clip.file.extension.slice(1)}`;
            
            await this.ffmpeg.writeFile(inputName, new Uint8Array(videoData));
            inputFiles.push({
                name: inputName,
                duration: clip.duration,
                startTime: clip.startTime
            });
        }

        if (onProgress) {
            onProgress(30, 'Processing video clips...');
        }

        // Create concat file for FFmpeg
        const concatContent = inputFiles.map(file => 
            `file '${file.name}'`
        ).join('\n');
        
        await this.ffmpeg.writeFile('concat.txt', concatContent);

        const outputName = `remix_${videoNumber}_${Date.now()}.mp4`;

        if (onProgress) {
            onProgress(50, 'Combining video clips...');
        }

        // Run FFmpeg command to concatenate videos
        await this.ffmpeg.exec([
            '-f', 'concat',
            '-safe', '0',
            '-i', 'concat.txt',
            '-c', 'copy',
            '-avoid_negative_ts', 'make_zero',
            outputName
        ]);

        if (onProgress) {
            onProgress(90, 'Finalizing video...');
        }

        // Read the output file
        const outputData = await this.ffmpeg.readFile(outputName);
        
        // Clean up temporary files
        for (const file of inputFiles) {
            try {
                await this.ffmpeg.deleteFile(file.name);
            } catch (e) {
                console.warn(`Failed to delete ${file.name}:`, e);
            }
        }
        
        try {
            await this.ffmpeg.deleteFile('concat.txt');
            await this.ffmpeg.deleteFile(outputName);
        } catch (e) {
            console.warn('Failed to clean up temporary files:', e);
        }

        if (onProgress) {
            onProgress(100, 'Complete!');
        }

        return {
            name: outputName,
            data: outputData,
            clips: selectedClips.map(clip => clip.file.name),
            duration: this.calculateTotalDuration(selectedClips)
        };
    }

    // Select random clips to match target duration
    selectRandomClips(videoFiles, targetDuration) {
        const clips = [];
        let totalDuration = 0;
        const usedFiles = new Set();

        // Estimate clip durations (we'll use random durations between 5-30 seconds)
        const availableFiles = videoFiles.filter(file => !usedFiles.has(file.name));
        
        while (totalDuration < targetDuration && availableFiles.length > 0) {
            // Pick a random file
            const randomIndex = Math.floor(Math.random() * availableFiles.length);
            const selectedFile = availableFiles[randomIndex];
            
            // Remove from available files to avoid duplicates
            availableFiles.splice(randomIndex, 1);
            usedFiles.add(selectedFile.name);

            // Generate random clip duration (5-30 seconds)
            const minClipDuration = 5;
            const maxClipDuration = Math.min(30, targetDuration - totalDuration + 5);
            const clipDuration = Math.random() * (maxClipDuration - minClipDuration) + minClipDuration;
            
            // Random start time (assume videos are at least 60 seconds, start within first 30 seconds)
            const startTime = Math.random() * 30;

            clips.push({
                file: selectedFile,
                duration: clipDuration,
                startTime: startTime
            });

            totalDuration += clipDuration;

            // If we're close to target duration, break
            if (totalDuration >= targetDuration * 0.9) {
                break;
            }
        }

        return clips;
    }

    // Load video file data
    async loadVideoFile(videoFile) {
        // This would be called from the FileManager
        return await window.fileManager.readVideoFile(videoFile);
    }

    // Calculate total duration of clips
    calculateTotalDuration(clips) {
        return clips.reduce((total, clip) => total + clip.duration, 0);
    }

    // Create download blob from video data
    createDownloadBlob(videoData) {
        return new Blob([videoData], { type: 'video/mp4' });
    }

    // Get processing status
    getStatus() {
        return {
            isLoaded: this.isLoaded,
            isProcessing: this.isProcessing
        };
    }
}

// Helper function for FFmpeg (from their examples)
const toBlobURL = async (url, mimeType) => {
    const response = await fetch(url);
    const buffer = await response.arrayBuffer();
    return URL.createObjectURL(new Blob([buffer], { type: mimeType }));
};

// Export for use in other modules
window.VideoProcessor = VideoProcessor;
