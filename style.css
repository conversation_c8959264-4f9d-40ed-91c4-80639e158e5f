* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

header h1 {
    font-size: 2.5em;
    color: #4a5568;
    margin-bottom: 10px;
}

header p {
    color: #718096;
    font-size: 1.1em;
}

.section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.section h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a6fd8;
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #38a169;
}

.info-box {
    margin-top: 15px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.info-box p {
    margin-bottom: 8px;
}

.video-list {
    max-height: 200px;
    overflow-y: auto;
    margin-top: 10px;
    padding: 10px;
    background: #f7fafc;
    border-radius: 4px;
}

.video-item {
    padding: 5px 0;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.9em;
    color: #4a5568;
}

.video-item:last-child {
    border-bottom: none;
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 600px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }
}

.setting-item {
    display: flex;
    flex-direction: column;
}

.setting-item label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #4a5568;
}

.setting-item input {
    padding: 10px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.setting-item input:focus {
    outline: none;
    border-color: #667eea;
}

.setting-item small {
    margin-top: 5px;
    color: #718096;
    font-size: 0.85em;
}

.progress-section {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    width: 0%;
    transition: width 0.3s ease;
}

#progressText {
    text-align: center;
    color: #4a5568;
    font-weight: 600;
}

.download-section {
    min-height: 100px;
}

.placeholder {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    padding: 40px 20px;
}

.download-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 6px;
    margin-bottom: 10px;
    border: 1px solid #e2e8f0;
}

.download-item:last-child {
    margin-bottom: 0;
}

.download-info {
    flex: 1;
}

.download-info h4 {
    color: #4a5568;
    margin-bottom: 5px;
}

.download-info p {
    color: #718096;
    font-size: 0.9em;
}

.btn-download {
    background: #ed8936;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
}

.btn-download:hover {
    background: #dd7724;
}

footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
    color: #718096;
    font-size: 0.9em;
}

.error {
    background: #fed7d7;
    color: #c53030;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    border-left: 4px solid #e53e3e;
}

.success {
    background: #c6f6d5;
    color: #22543d;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    border-left: 4px solid #38a169;
}
