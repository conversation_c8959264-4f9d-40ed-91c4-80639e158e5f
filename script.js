// Main application logic
class LazyRemixer {
    constructor() {
        this.fileManager = new FileManager();
        this.videoProcessor = new VideoProcessor();
        this.generatedVideos = [];
        
        this.initializeElements();
        this.bindEvents();
        this.checkBrowserSupport();
    }

    // Initialize DOM elements
    initializeElements() {
        this.elements = {
            selectFolderBtn: document.getElementById('selectFolderBtn'),
            folderInfo: document.getElementById('folderInfo'),
            folderPath: document.getElementById('folderPath'),
            videoCount: document.getElementById('videoCount'),
            videoList: document.getElementById('videoList'),
            duration: document.getElementById('duration'),
            quantity: document.getElementById('quantity'),
            generateBtn: document.getElementById('generateBtn'),
            progressSection: document.getElementById('progressSection'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            downloadSection: document.getElementById('downloadSection')
        };
    }

    // Bind event listeners
    bindEvents() {
        this.elements.selectFolderBtn.addEventListener('click', () => this.selectFolder());
        this.elements.generateBtn.addEventListener('click', () => this.generateVideos());
        
        // Enable generate button when folder is selected and inputs are valid
        [this.elements.duration, this.elements.quantity].forEach(input => {
            input.addEventListener('input', () => this.validateInputs());
        });
    }

    // Check browser support
    checkBrowserSupport() {
        if (!this.fileManager.isSupported()) {
            this.showError('Your browser does not support the File System Access API. Please use Chrome 86+ or Edge 86+.');
            this.elements.selectFolderBtn.disabled = true;
        }
    }

    // Select video folder
    async selectFolder() {
        try {
            this.elements.selectFolderBtn.disabled = true;
            this.elements.selectFolderBtn.textContent = 'Selecting...';

            const folderInfo = await this.fileManager.selectFolder();
            
            this.displayFolderInfo(folderInfo);
            this.validateInputs();
            
            this.showSuccess(`Found ${folderInfo.videoCount} video files in "${folderInfo.name}"`);
        } catch (error) {
            this.showError(error.message);
        } finally {
            this.elements.selectFolderBtn.disabled = false;
            this.elements.selectFolderBtn.textContent = 'Choose Video Folder';
        }
    }

    // Display folder information
    displayFolderInfo(folderInfo) {
        this.elements.folderPath.textContent = folderInfo.name;
        this.elements.videoCount.textContent = folderInfo.videoCount;
        
        // Display video list
        this.elements.videoList.innerHTML = '';
        folderInfo.videos.forEach(video => {
            const videoItem = document.createElement('div');
            videoItem.className = 'video-item';
            videoItem.textContent = `${video.name} (${video.extension})`;
            this.elements.videoList.appendChild(videoItem);
        });
        
        this.elements.folderInfo.style.display = 'block';
    }

    // Validate inputs and enable/disable generate button
    validateInputs() {
        const duration = parseInt(this.elements.duration.value);
        const quantity = parseInt(this.elements.quantity.value);
        const hasVideos = this.fileManager.videoFiles.length > 0;
        
        const isValid = hasVideos && 
                       duration >= 10 && duration <= 600 &&
                       quantity >= 1 && quantity <= 10;
        
        this.elements.generateBtn.disabled = !isValid;
    }

    // Generate random videos
    async generateVideos() {
        try {
            const duration = parseInt(this.elements.duration.value);
            const quantity = parseInt(this.elements.quantity.value);
            
            if (this.fileManager.videoFiles.length === 0) {
                throw new Error('No video files selected');
            }

            this.elements.generateBtn.disabled = true;
            this.elements.generateBtn.textContent = 'Generating...';
            this.showProgress(0, 'Initializing...');

            // Initialize video processor if not already done
            if (!this.videoProcessor.isLoaded) {
                await this.videoProcessor.initialize((progress, message) => {
                    this.showProgress(progress * 0.1, `Loading FFmpeg: ${message}`);
                });
            }

            // Generate videos
            const results = await this.videoProcessor.generateRandomVideos(
                this.fileManager.videoFiles,
                duration,
                quantity,
                (progress, message) => this.showProgress(progress, message)
            );

            this.generatedVideos = results;
            this.displayDownloads(results);
            this.hideProgress();
            
            this.showSuccess(`Successfully generated ${results.length} video(s)!`);
        } catch (error) {
            this.showError(error.message);
            this.hideProgress();
        } finally {
            this.elements.generateBtn.disabled = false;
            this.elements.generateBtn.textContent = 'Generate Random Videos';
        }
    }

    // Show progress
    showProgress(percentage, message) {
        this.elements.progressSection.style.display = 'block';
        this.elements.progressFill.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
        this.elements.progressText.textContent = message;
    }

    // Hide progress
    hideProgress() {
        this.elements.progressSection.style.display = 'none';
    }

    // Display download links
    displayDownloads(videos) {
        this.elements.downloadSection.innerHTML = '';
        
        videos.forEach((video, index) => {
            const downloadItem = document.createElement('div');
            downloadItem.className = 'download-item';
            
            const info = document.createElement('div');
            info.className = 'download-info';
            
            const title = document.createElement('h4');
            title.textContent = `Random Video ${index + 1}`;
            
            const details = document.createElement('p');
            details.textContent = `Duration: ${Math.round(video.duration)}s | Clips: ${video.clips.length} | Size: ${this.formatFileSize(video.data.length)}`;
            
            const clipsList = document.createElement('p');
            clipsList.textContent = `Clips used: ${video.clips.join(', ')}`;
            clipsList.style.fontSize = '0.8em';
            clipsList.style.color = '#718096';
            
            info.appendChild(title);
            info.appendChild(details);
            info.appendChild(clipsList);
            
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'btn btn-download';
            downloadBtn.textContent = 'Download';
            downloadBtn.onclick = () => this.downloadVideo(video, index + 1);
            
            downloadItem.appendChild(info);
            downloadItem.appendChild(downloadBtn);
            this.elements.downloadSection.appendChild(downloadItem);
        });
    }

    // Download video
    downloadVideo(video, number) {
        const blob = this.videoProcessor.createDownloadBlob(video.data);
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `LazyRemix_${number}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.mp4`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // Clean up the URL object
        setTimeout(() => URL.revokeObjectURL(url), 1000);
    }

    // Format file size
    formatFileSize(bytes) {
        return this.fileManager.formatFileSize(bytes);
    }

    // Show error message
    showError(message) {
        this.removeMessages();
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error';
        errorDiv.textContent = message;
        document.querySelector('.container').insertBefore(errorDiv, document.querySelector('main'));
        
        // Auto-remove after 5 seconds
        setTimeout(() => this.removeMessages(), 5000);
    }

    // Show success message
    showSuccess(message) {
        this.removeMessages();
        const successDiv = document.createElement('div');
        successDiv.className = 'success';
        successDiv.textContent = message;
        document.querySelector('.container').insertBefore(successDiv, document.querySelector('main'));
        
        // Auto-remove after 3 seconds
        setTimeout(() => this.removeMessages(), 3000);
    }

    // Remove all messages
    removeMessages() {
        document.querySelectorAll('.error, .success').forEach(el => el.remove());
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LazyRemixer();
});
