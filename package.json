{"name": "lazy-remixer", "version": "1.0.0", "description": "A web app for randomly combining video files into new videos", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "docker:build": "docker build -t lazy-remixer .", "docker:run": "docker run -p 3000:3000 lazy-remixer", "docker:compose": "docker-compose up -d", "docker:stop": "docker-compose down"}, "keywords": ["video", "remix", "ffmpeg", "web-app"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {}}