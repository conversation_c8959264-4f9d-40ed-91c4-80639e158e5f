<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LazyRemixer - Random Video Combiner</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎬 LazyRemixer</h1>
            <p>Randomly combine your video files into new creations</p>
        </header>

        <main>
            <div class="section">
                <h2>📁 Select Video Folder</h2>
                <button id="selectFolderBtn" class="btn btn-primary">
                    Choose Video Folder
                </button>
                <div id="folderInfo" class="info-box" style="display: none;">
                    <p><strong>Selected folder:</strong> <span id="folderPath"></span></p>
                    <p><strong>Video files found:</strong> <span id="videoCount">0</span></p>
                    <div id="videoList" class="video-list"></div>
                </div>
            </div>

            <div class="section">
                <h2>⚙️ Settings</h2>
                <div class="settings-grid">
                    <div class="setting-item">
                        <label for="duration">Target Duration (seconds):</label>
                        <input type="number" id="duration" min="10" max="600" value="60" required>
                        <small>10-600 seconds (10 minutes max)</small>
                    </div>
                    <div class="setting-item">
                        <label for="quantity">Number of Videos to Generate:</label>
                        <input type="number" id="quantity" min="1" max="10" value="1" required>
                        <small>1-10 videos max</small>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🎯 Generate</h2>
                <button id="generateBtn" class="btn btn-success" disabled>
                    Generate Random Videos
                </button>
                <div id="progressSection" class="progress-section" style="display: none;">
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <p id="progressText">Preparing...</p>
                </div>
            </div>

            <div class="section">
                <h2>📥 Downloads</h2>
                <div id="downloadSection" class="download-section">
                    <p class="placeholder">Generated videos will appear here for download</p>
                </div>
            </div>
        </main>

        <footer>
            <p>⚠️ This app requires a modern browser with File System Access API support (Chrome 86+, Edge 86+)</p>
        </footer>
    </div>

    <!-- Load ffmpeg.wasm -->
    <script src="https://unpkg.com/@ffmpeg/ffmpeg@0.12.7/dist/umd/ffmpeg.js"></script>
    <script src="https://unpkg.com/@ffmpeg/util@0.12.1/dist/umd/index.js"></script>
    
    <!-- Load our modules -->
    <script src="file-manager.js"></script>
    <script src="video-processor.js"></script>
    <script src="script.js"></script>
</body>
</html>
